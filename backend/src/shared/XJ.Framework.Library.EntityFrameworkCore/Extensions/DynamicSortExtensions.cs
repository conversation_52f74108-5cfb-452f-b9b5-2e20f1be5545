using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.Library.EntityFrameworkCore.Extensions;

/// <summary>
/// 动态排序扩展方法
/// </summary>
public static class DynamicSortExtensions
{
    /// <summary>
    /// 应用动态连接排序
    /// </summary>
    /// <typeparam name="TEntity">主实体类型</typeparam>
    /// <typeparam name="TJoinEntity">连接实体类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="dbContext">数据库上下文</param>
    /// <param name="joinCondition">连接条件</param>
    /// <param name="matchCondition">匹配条件（用于匹配字段代码）</param>
    /// <param name="sortSelector">排序字段选择器</param>
    /// <param name="fieldCode">字段代码</param>
    /// <param name="sortDirection">排序方向</param>
    /// <param name="isFirst">是否为第一个排序条件</param>
    /// <returns>排序后的查询对象</returns>
    public static IQueryable<TEntity> ApplyDynamicJoinSort<TEntity, TJoinEntity>(
        this IQueryable<TEntity> queryable,
        DbContext dbContext,
        Expression<Func<TEntity, TJoinEntity, bool>> joinCondition,
        Expression<Func<TJoinEntity, string, bool>> matchCondition,
        Expression<Func<TJoinEntity, object>> sortSelector,
        string fieldCode,
        SortDirection sortDirection,
        bool isFirst = false) 
        where TEntity : class 
        where TJoinEntity : class
    {
        // 构建排序表达式
        Expression<Func<TEntity, object>> sortExpression = entity =>
            dbContext.Set<TJoinEntity>()
                .Where(joinEntity => joinCondition.Compile()(entity, joinEntity))
                .Where(joinEntity => matchCondition.Compile()(joinEntity, fieldCode))
                .Select(sortSelector)
                .FirstOrDefault() ?? string.Empty;

        // 应用排序
        if (isFirst)
        {
            return sortDirection == SortDirection.Ascending
                ? queryable.OrderBy(sortExpression)
                : queryable.OrderByDescending(sortExpression);
        }
        else
        {
            var orderedQueryable = (IOrderedQueryable<TEntity>)queryable;
            return sortDirection == SortDirection.Ascending
                ? orderedQueryable.ThenBy(sortExpression)
                : orderedQueryable.ThenByDescending(sortExpression);
        }
    }

    /// <summary>
    /// 应用多个动态连接排序
    /// </summary>
    /// <typeparam name="TEntity">主实体类型</typeparam>
    /// <typeparam name="TJoinEntity">连接实体类型</typeparam>
    /// <param name="queryable">查询对象</param>
    /// <param name="dbContext">数据库上下文</param>
    /// <param name="joinCondition">连接条件</param>
    /// <param name="matchCondition">匹配条件</param>
    /// <param name="sortSelector">排序字段选择器</param>
    /// <param name="sortFields">排序字段字典（字段代码 -> 排序方向）</param>
    /// <param name="isFirst">是否为第一组排序条件</param>
    /// <returns>排序后的查询对象</returns>
    public static IQueryable<TEntity> ApplyDynamicJoinSorts<TEntity, TJoinEntity>(
        this IQueryable<TEntity> queryable,
        DbContext dbContext,
        Expression<Func<TEntity, TJoinEntity, bool>> joinCondition,
        Expression<Func<TJoinEntity, string, bool>> matchCondition,
        Expression<Func<TJoinEntity, object>> sortSelector,
        Dictionary<string, SortDirection> sortFields,
        bool isFirst = false)
        where TEntity : class 
        where TJoinEntity : class
    {
        if (sortFields == null || !sortFields.Any())
            return queryable;

        var result = queryable;
        var firstSort = isFirst;

        foreach (var sortField in sortFields)
        {
            result = result.ApplyDynamicJoinSort<TEntity, TJoinEntity>(
                dbContext,
                joinCondition,
                matchCondition,
                sortSelector,
                sortField.Key,
                sortField.Value,
                firstSort);
            firstSort = false;
        }

        return result;
    }
}
