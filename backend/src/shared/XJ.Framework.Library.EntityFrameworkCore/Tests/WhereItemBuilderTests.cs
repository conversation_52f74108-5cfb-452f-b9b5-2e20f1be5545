using XJ.Framework.Library.EntityFrameworkCore.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.Enums;
using XJ.Framework.Library.EntityFrameworkCore.SQL;

namespace XJ.Framework.Library.EntityFrameworkCore.Tests;

/// <summary>
/// WhereItemBuilder测试类
/// </summary>
public class WhereItemBuilderTests
{
    /// <summary>
    /// 测试基本WHERE条件构建
    /// </summary>
    public void TestBasicWhereConditions()
    {
        var builder = new WhereItemBuilder("t");
        var whereItems = new List<WhereItem>
        {
            new WhereItem { Field = "Name", Operator = WhereOperator.Equal, Value = "test" },
            new WhereItem { Field = "Age", Operator = WhereOperator.GreaterThan, Value = 18 },
            new WhereItem { Field = "IsActive", Operator = WhereOperator.Equal, Value = true }
        };

        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        // 验证WHERE子句
        // Assert.Contains("t.name = @p0", whereClause);
        // Assert.Contains("t.age > @p1", whereClause);
        // Assert.Contains("t.is_active = @p2", whereClause);
        // Assert.Contains("AND", whereClause);

        // 验证参数
        // Assert.Equal(3, parameters.Count);
        // Assert.Equal("test", parameters["@p0"]);
        // Assert.Equal(18, parameters["@p1"]);
        // Assert.Equal(true, parameters["@p2"]);
    }

    /// <summary>
    /// 测试字符串操作符
    /// </summary>
    public void TestStringOperators()
    {
        var builder = new WhereItemBuilder("t");
        var whereItems = new List<WhereItem>
        {
            new WhereItem { Field = "Name", Operator = WhereOperator.Contains, Value = "test" },
            new WhereItem { Field = "Title", Operator = WhereOperator.StartsWith, Value = "prefix" },
            new WhereItem { Field = "Description", Operator = WhereOperator.EndsWith, Value = "suffix" }
        };

        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        // 验证LIKE操作
        // Assert.Contains("t.name LIKE @p0", whereClause);
        // Assert.Contains("t.title LIKE @p1", whereClause);
        // Assert.Contains("t.description LIKE @p2", whereClause);

        // 验证LIKE参数值
        // Assert.Equal("%test%", parameters["@p0"]);
        // Assert.Equal("prefix%", parameters["@p1"]);
        // Assert.Equal("%suffix", parameters["@p2"]);
    }

    /// <summary>
    /// 测试IN操作符
    /// </summary>
    public void TestInOperator()
    {
        var builder = new WhereItemBuilder("t");
        var whereItems = new List<WhereItem>
        {
            new WhereItem 
            { 
                Field = "Status", 
                Operator = WhereOperator.In, 
                Value = new List<int> { 1, 2, 3 } 
            }
        };

        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        // 验证IN子句
        // Assert.Contains("t.status IN (@p0, @p1, @p2)", whereClause);

        // 验证参数
        // Assert.Equal(3, parameters.Count);
        // Assert.Equal(1, parameters["@p0"]);
        // Assert.Equal(2, parameters["@p1"]);
        // Assert.Equal(3, parameters["@p2"]);
    }

    /// <summary>
    /// 测试BETWEEN操作符
    /// </summary>
    public void TestBetweenOperator()
    {
        var builder = new WhereItemBuilder("t");
        var whereItems = new List<WhereItem>
        {
            new WhereItem 
            { 
                Field = "CreatedDate", 
                Operator = WhereOperator.Between, 
                Value = new List<DateTime> 
                { 
                    new DateTime(2023, 1, 1), 
                    new DateTime(2023, 12, 31) 
                } 
            }
        };

        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        // 验证BETWEEN子句
        // Assert.Contains("t.created_date BETWEEN @p0 AND @p1", whereClause);

        // 验证参数
        // Assert.Equal(2, parameters.Count);
        // Assert.Equal(new DateTime(2023, 1, 1), parameters["@p0"]);
        // Assert.Equal(new DateTime(2023, 12, 31), parameters["@p1"]);
    }

    /// <summary>
    /// 测试空条件处理
    /// </summary>
    public void TestEmptyConditions()
    {
        var builder = new WhereItemBuilder("t");
        var whereItems = new List<WhereItem>();

        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        // 空条件应该返回默认条件
        // Assert.Equal("1=1", whereClause);
        // Assert.Empty(parameters);
    }

    /// <summary>
    /// 测试属性名转换
    /// </summary>
    public void TestPropertyNameConversion()
    {
        var builder = new WhereItemBuilder("entity");
        var whereItems = new List<WhereItem>
        {
            new WhereItem { Field = "FirstName", Operator = WhereOperator.Equal, Value = "John" },
            new WhereItem { Field = "LastModifiedTime", Operator = WhereOperator.GreaterThan, Value = DateTime.Now }
        };

        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        // 验证Pascal命名转换为snake_case
        // Assert.Contains("entity.first_name", whereClause);
        // Assert.Contains("entity.last_modified_time", whereClause);
    }

    /// <summary>
    /// 测试复杂查询条件组合
    /// </summary>
    public void TestComplexWhereConditions()
    {
        var builder = new WhereItemBuilder("u");
        var whereItems = new List<WhereItem>
        {
            new WhereItem { Field = "Name", Operator = WhereOperator.Contains, Value = "admin" },
            new WhereItem { Field = "Age", Operator = WhereOperator.Between, Value = new List<int> { 25, 65 } },
            new WhereItem { Field = "Status", Operator = WhereOperator.In, Value = new List<string> { "Active", "Pending" } },
            new WhereItem { Field = "IsDeleted", Operator = WhereOperator.Equal, Value = false }
        };

        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        // 验证复杂条件组合
        // Assert.Contains("u.name LIKE", whereClause);
        // Assert.Contains("u.age BETWEEN", whereClause);
        // Assert.Contains("u.status IN", whereClause);
        // Assert.Contains("u.is_deleted =", whereClause);
        // Assert.Equal(3, whereClause.Split("AND").Length); // 4个条件用3个AND连接

        // 验证参数数量
        // Assert.Equal(6, parameters.Count); // Contains(1) + Between(2) + In(2) + Equal(1) = 6
    }
}
