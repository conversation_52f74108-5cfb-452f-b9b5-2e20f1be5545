using XJ.Framework.Library.EntityFrameworkCore.Enums;

namespace XJ.Framework.Library.EntityFrameworkCore.Dtos;

public class WhereItem
{
    /// <summary>
    /// 实体属性名
    /// </summary>
    public string Field { get; set; } = null!;

    /// <summary>
    /// 数据库列名
    /// </summary>
    public string DataField { get; set; } = null!;

    /// <summary>
    /// 查询操作符
    /// </summary>
    public WhereOperator Operator { get; set; }

    /// <summary>
    /// 查询值
    /// </summary>
    public object? Value { get; set; }
}
