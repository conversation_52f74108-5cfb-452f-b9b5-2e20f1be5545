using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;

/// <summary>
/// 动态连接排序构建器
/// </summary>
/// <typeparam name="TEntity">主实体类型</typeparam>
public class DynamicJoinSortBuilder<TEntity> where TEntity : class
{
    private readonly DbContext _dbContext;

    public DynamicJoinSortBuilder(DbContext dbContext)
    {
        _dbContext = dbContext;
    }

    /// <summary>
    /// 应用动态连接排序
    /// </summary>
    public IQueryable<TEntity> ApplyDynamicJoinSort<TJoinEntity>(
        IQueryable<TEntity> queryable,
        List<DynamicJoinSortCriteria<TEntity, TJoinEntity>> sortCriteria) where TJoinEntity : class
    {
        if (sortCriteria == null || !sortCriteria.Any())
            return queryable;

        IOrderedQueryable<TEntity>? orderedQueryable = null;

        foreach (var criteria in sortCriteria)
        {
            var sortExpression = BuildJoinSortExpression(criteria);

            if (orderedQueryable == null)
            {
                orderedQueryable = criteria.SortDirection == SortDirection.Ascending
                    ? queryable.OrderBy(sortExpression)
                    : queryable.OrderByDescending(sortExpression);
            }
            else
            {
                orderedQueryable = criteria.SortDirection == SortDirection.Ascending
                    ? orderedQueryable.ThenBy(sortExpression)
                    : orderedQueryable.ThenByDescending(sortExpression);
            }
        }

        return orderedQueryable ?? queryable;
    }

    /// <summary>
    /// 应用简化的动态排序
    /// </summary>
    public IQueryable<TEntity> ApplySimpleDynamicSort(
        IQueryable<TEntity> queryable,
        List<SimpleDynamicSortCriteria> sortCriteria)
    {
        if (sortCriteria == null || !sortCriteria.Any())
            return queryable;

        IOrderedQueryable<TEntity>? orderedQueryable = null;

        foreach (var criteria in sortCriteria)
        {
            var sortExpression = BuildSimpleSortExpression(criteria);

            if (orderedQueryable == null)
            {
                orderedQueryable = criteria.SortDirection == SortDirection.Ascending
                    ? queryable.OrderBy(sortExpression)
                    : queryable.OrderByDescending(sortExpression);
            }
            else
            {
                orderedQueryable = criteria.SortDirection == SortDirection.Ascending
                    ? orderedQueryable.ThenBy(sortExpression)
                    : orderedQueryable.ThenByDescending(sortExpression);
            }
        }

        return orderedQueryable ?? queryable;
    }

    private Expression<Func<TEntity, object>> BuildJoinSortExpression<TJoinEntity>(
        DynamicJoinSortCriteria<TEntity, TJoinEntity> criteria) where TJoinEntity : class
    {
        return mainEntity => _dbContext.Set<TJoinEntity>()
            .Where(joinEntity => criteria.JoinCondition.Compile()(mainEntity, joinEntity))
            .Where(joinEntity => criteria.MatchCondition.Compile()(joinEntity, criteria.FieldCode))
            .Select(criteria.SortFieldSelector)
            .FirstOrDefault() ?? string.Empty;
    }

    private Expression<Func<TEntity, object>> BuildSimpleSortExpression(SimpleDynamicSortCriteria criteria)
    {
        // 这里需要根据具体的实体类型来构建表达式
        // 由于是通用方法，这里提供一个基础实现
        // 具体的实现应该在派生类中重写
        return entity => string.Empty;
    }
}

/// <summary>
/// FormInstance专用的动态排序构建器
/// </summary>
public class FormInstanceDynamicSortBuilder : DynamicJoinSortBuilder<object>
{
    public FormInstanceDynamicSortBuilder(DbContext dbContext) : base(dbContext)
    {
    }

    /// <summary>
    /// 为FormInstance构建简化的排序表达式
    /// </summary>
    public Expression<Func<TEntity, object>> BuildFormInstanceSortExpression<TEntity>(
        SimpleDynamicSortCriteria criteria,
        DbContext dbContext) where TEntity : class
    {
        // 根据连接表名决定使用哪个表进行排序
        if (criteria.JoinTableName == "form_instance_datas")
        {
            return BuildFormInstanceDataSortExpression<TEntity>(criteria, dbContext);
        }
        else if (criteria.JoinTableName == "form_field_instances")
        {
            return BuildFormFieldInstanceSortExpression<TEntity>(criteria, dbContext);
        }

        throw new NotSupportedException($"Join table {criteria.JoinTableName} is not supported");
    }

    private Expression<Func<TEntity, object>> BuildFormInstanceDataSortExpression<TEntity>(
        SimpleDynamicSortCriteria criteria,
        DbContext dbContext) where TEntity : class
    {
        // 这里需要使用反射或动态表达式构建
        // 简化实现，实际使用时需要根据TEntity的具体类型来构建
        return entity => string.Empty;
    }

    private Expression<Func<TEntity, object>> BuildFormFieldInstanceSortExpression<TEntity>(
        SimpleDynamicSortCriteria criteria,
        DbContext dbContext) where TEntity : class
    {
        // 这里需要使用反射或动态表达式构建
        // 简化实现，实际使用时需要根据TEntity的具体类型来构建
        return entity => string.Empty;
    }
}
