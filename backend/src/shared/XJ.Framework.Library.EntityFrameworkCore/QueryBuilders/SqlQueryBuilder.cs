using System.Text;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;

/// <summary>
/// SQL查询构建器
/// </summary>
public class SqlQueryBuilder
{
    /// <summary>
    /// 构建带动态排序的分页查询SQL
    /// </summary>
    /// <param name="mainTableName">主表名</param>
    /// <param name="mainTableAlias">主表别名</param>
    /// <param name="whereClause">WHERE条件</param>
    /// <param name="dynamicSorts">动态排序条件</param>
    /// <param name="rowIndex">起始行索引</param>
    /// <param name="pageSize">页面大小</param>
    /// <param name="additionalParameters">额外参数</param>
    /// <returns>查询SQL和参数</returns>
    public (string Sql, Dictionary<string, object> Parameters) BuildPagedQueryWithDynamicSort(
        string mainTableName,
        string mainTableAlias,
        string whereClause,
        List<DynamicSortInfo> dynamicSorts,
        int rowIndex,
        int pageSize,
        Dictionary<string, object>? additionalParameters = null)
    {
        var parameters = new Dictionary<string, object>(additionalParameters ?? new Dictionary<string, object>())
        {
            ["@RowIndex"] = rowIndex,
            ["@PageSize"] = pageSize
        };

        var sql = new StringBuilder();
        sql.AppendLine("WITH SortedData AS (");
        sql.AppendLine($"    SELECT {mainTableAlias}.*,");

        // 添加动态排序字段
        for (int i = 0; i < dynamicSorts.Count; i++)
        {
            var sort = dynamicSorts[i];
            var paramName = $"@FieldCode{i}";
            parameters[paramName] = sort.FieldCode;

            sql.AppendLine($"           (SELECT TOP 1 {sort.SortFieldName}");
            sql.AppendLine($"            FROM [{sort.SchemaName}].[{sort.JoinTableName}] jt{i}");
            sql.Append($"            WHERE ");

            // 构建连接条件
            var joinConditions = new List<string>();
            foreach (var mapping in sort.JoinFieldMappings)
            {
                joinConditions.Add($"jt{i}.{mapping.Value} = {mainTableAlias}.{ConvertToSnakeCase(mapping.Key)}");
            }
            sql.Append(string.Join(" AND ", joinConditions));

            // 添加匹配条件
            sql.AppendLine($" AND jt{i}.{sort.MatchFieldName} = {paramName}");

            // 添加软删除条件
            if (!string.IsNullOrEmpty(sort.DeletedFieldName))
            {
                sql.AppendLine($"              AND jt{i}.{sort.DeletedFieldName} = 0");
            }

            sql.AppendLine($"           ) AS SortField{i},");
        }

        sql.AppendLine("           ROW_NUMBER() OVER (");
        sql.Append("               ORDER BY ");

        // 构建ORDER BY子句
        if (dynamicSorts.Any())
        {
            var orderByParts = new List<string>();
            for (int i = 0; i < dynamicSorts.Count; i++)
            {
                var sort = dynamicSorts[i];
                var direction = sort.SortDirection == SortDirection.Ascending ? "ASC" : "DESC";
                orderByParts.Add($"SortField{i} {direction}");
            }
            sql.Append(string.Join(", ", orderByParts));
        }
        else
        {
            sql.Append($"{mainTableAlias}.id");
        }

        sql.AppendLine(") AS RowNum");
        sql.AppendLine($"    FROM [{GetSchemaFromTableName(mainTableName)}].[{mainTableName}] {mainTableAlias}");

        if (!string.IsNullOrEmpty(whereClause))
        {
            sql.AppendLine($"    WHERE {whereClause}");
        }

        sql.AppendLine(")");
        sql.AppendLine("SELECT * FROM SortedData");
        sql.AppendLine("WHERE RowNum > @RowIndex AND RowNum <= (@RowIndex + @PageSize)");
        sql.AppendLine("ORDER BY RowNum");

        return (sql.ToString(), parameters);
    }

    /// <summary>
    /// 构建计数查询SQL
    /// </summary>
    public (string Sql, Dictionary<string, object> Parameters) BuildCountQuery(
        string mainTableName,
        string mainTableAlias,
        string whereClause,
        Dictionary<string, object>? additionalParameters = null)
    {
        var parameters = new Dictionary<string, object>(additionalParameters ?? new Dictionary<string, object>());
        var sql = new StringBuilder();

        sql.AppendLine($"SELECT COUNT(1) FROM [{GetSchemaFromTableName(mainTableName)}].[{mainTableName}] {mainTableAlias}");

        if (!string.IsNullOrEmpty(whereClause))
        {
            sql.AppendLine($"WHERE {whereClause}");
        }

        return (sql.ToString(), parameters);
    }

    /// <summary>
    /// 从表名中获取Schema
    /// </summary>
    private string GetSchemaFromTableName(string tableName)
    {
        // 根据表名推断Schema，这里可以根据实际情况调整
        return tableName.StartsWith("form_") ? "d" : "dbo";
    }

    /// <summary>
    /// 将Pascal命名转换为snake_case
    /// </summary>
    private string ConvertToSnakeCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var result = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            if (i > 0 && char.IsUpper(input[i]))
            {
                result.Append('_');
            }
            result.Append(char.ToLower(input[i]));
        }
        return result.ToString();
    }
}

/// <summary>
/// 动态排序信息
/// </summary>
public class DynamicSortInfo
{
    /// <summary>
    /// 字段代码
    /// </summary>
    public string FieldCode { get; set; } = null!;

    /// <summary>
    /// 排序方向
    /// </summary>
    public SortDirection SortDirection { get; set; }

    /// <summary>
    /// 连接表名
    /// </summary>
    public string JoinTableName { get; set; } = null!;

    /// <summary>
    /// Schema名
    /// </summary>
    public string SchemaName { get; set; } = "d";

    /// <summary>
    /// 连接条件字段映射（主表字段 -> 连接表字段）
    /// </summary>
    public Dictionary<string, string> JoinFieldMappings { get; set; } = new();

    /// <summary>
    /// 匹配字段名（连接表中用于匹配FieldCode的字段）
    /// </summary>
    public string MatchFieldName { get; set; } = null!;

    /// <summary>
    /// 排序字段名（连接表中用于排序的字段）
    /// </summary>
    public string SortFieldName { get; set; } = null!;

    /// <summary>
    /// 连接表的软删除字段名（可选）
    /// </summary>
    public string? DeletedFieldName { get; set; }
}
