using System.Linq.Expressions;
using System.Text;

namespace XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;

/// <summary>
/// 表达式到SQL转换器
/// </summary>
public class ExpressionToSqlConverter
{
    private readonly Dictionary<string, object> _parameters = new();
    private readonly string _tableAlias;
    private int _parameterIndex = 0;

    public ExpressionToSqlConverter(string tableAlias = "t")
    {
        _tableAlias = tableAlias;
    }

    /// <summary>
    /// 将Lambda表达式转换为SQL WHERE条件
    /// </summary>
    public (string WhereClause, Dictionary<string, object> Parameters) Convert<T>(Expression<Func<T, bool>> expression)
    {
        _parameters.Clear();
        _parameterIndex = 0;

        try
        {
            var whereClause = VisitExpression(expression.Body);
            return (whereClause, new Dictionary<string, object>(_parameters));
        }
        catch (Exception)
        {
            // 如果转换失败，返回默认条件
            return ("1=1", new Dictionary<string, object>());
        }
    }

    private string VisitExpression(Expression expression)
    {
        return expression switch
        {
            BinaryExpression binary => VisitBinaryExpression(binary),
            MemberExpression member => VisitMemberExpression(member),
            ConstantExpression constant => VisitConstantExpression(constant),
            MethodCallExpression method => VisitMethodCallExpression(method),
            UnaryExpression unary => VisitUnaryExpression(unary),
            _ => throw new NotSupportedException($"Expression type {expression.GetType().Name} is not supported")
        };
    }

    private string VisitBinaryExpression(BinaryExpression binary)
    {
        var left = VisitExpression(binary.Left);
        var right = VisitExpression(binary.Right);

        return binary.NodeType switch
        {
            ExpressionType.AndAlso => $"({left} AND {right})",
            ExpressionType.OrElse => $"({left} OR {right})",
            ExpressionType.Equal => $"{left} = {right}",
            ExpressionType.NotEqual => $"{left} <> {right}",
            ExpressionType.GreaterThan => $"{left} > {right}",
            ExpressionType.GreaterThanOrEqual => $"{left} >= {right}",
            ExpressionType.LessThan => $"{left} < {right}",
            ExpressionType.LessThanOrEqual => $"{left} <= {right}",
            _ => throw new NotSupportedException($"Binary operator {binary.NodeType} is not supported")
        };
    }

    private string VisitMemberExpression(MemberExpression member)
    {
        if (member.Expression is ParameterExpression)
        {
            // 这是实体的属性访问
            return GetColumnName(member.Member.Name);
        }

        // 这是常量或变量的访问，需要计算值
        var value = GetMemberValue(member);
        return CreateParameter(value);
    }

    private string VisitConstantExpression(ConstantExpression constant)
    {
        return CreateParameter(constant.Value);
    }

    private string VisitMethodCallExpression(MethodCallExpression method)
    {
        if (method.Method.Name == "Contains" && method.Object != null)
        {
            var obj = VisitExpression(method.Object);
            var arg = VisitExpression(method.Arguments[0]);
            return $"{obj} LIKE '%' + {arg} + '%'";
        }

        if (method.Method.Name == "StartsWith" && method.Object != null)
        {
            var obj = VisitExpression(method.Object);
            var arg = VisitExpression(method.Arguments[0]);
            return $"{obj} LIKE {arg} + '%'";
        }

        if (method.Method.Name == "EndsWith" && method.Object != null)
        {
            var obj = VisitExpression(method.Object);
            var arg = VisitExpression(method.Arguments[0]);
            return $"{obj} LIKE '%' + {arg}";
        }

        if (method.Method.Name == "Equals" && method.Object != null)
        {
            var obj = VisitExpression(method.Object);
            var arg = VisitExpression(method.Arguments[0]);
            return $"{obj} = {arg}";
        }

        if (method.Method.Name == "ToLower" && method.Object != null)
        {
            var obj = VisitExpression(method.Object);
            return $"LOWER({obj})";
        }

        // 处理Any方法（用于子查询）
        if (method.Method.Name == "Any")
        {
            // 简化处理，实际项目中可能需要更复杂的逻辑
            return "1=1";
        }

        throw new NotSupportedException($"Method {method.Method.Name} is not supported");
    }

    private string VisitUnaryExpression(UnaryExpression unary)
    {
        return unary.NodeType switch
        {
            ExpressionType.Not => $"NOT ({VisitExpression(unary.Operand)})",
            ExpressionType.Convert => VisitExpression(unary.Operand),
            _ => throw new NotSupportedException($"Unary operator {unary.NodeType} is not supported")
        };
    }

    private object? GetMemberValue(MemberExpression member)
    {
        var objectMember = Expression.Convert(member, typeof(object));
        var getterLambda = Expression.Lambda<Func<object>>(objectMember);
        var getter = getterLambda.Compile();
        return getter();
    }

    private string CreateParameter(object? value)
    {
        var paramName = $"@p{_parameterIndex++}";
        _parameters[paramName] = value ?? DBNull.Value;
        return paramName;
    }

    private string GetColumnName(string propertyName)
    {
        // 将C#属性名转换为数据库列名
        return $"{_tableAlias}.{ConvertToSnakeCase(propertyName)}";
    }

    private string ConvertToSnakeCase(string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var result = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            if (i > 0 && char.IsUpper(input[i]))
            {
                result.Append('_');
            }
            result.Append(char.ToLower(input[i]));
        }
        return result.ToString();
    }
}
