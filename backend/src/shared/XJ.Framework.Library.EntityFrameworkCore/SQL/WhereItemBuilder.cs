using System.Text;
using XJ.Framework.Library.Domain.Dtos;
using XJ.Framework.Library.Domain.Enums;

namespace XJ.Framework.Library.EntityFrameworkCore.SQL;

public class WhereItemBuilder
{
    private readonly string _tableAlias;
    private readonly Dictionary<string, object> _parameters = new();
    private int _parameterIndex = 0;

    public WhereItemBuilder(string tableAlias = "t")
    {
        _tableAlias = tableAlias;
    }

    /// <summary>
    /// 将WhereItem集合转换为SQL WHERE条件字符串
    /// </summary>
    /// <param name="whereItems">WHERE条件项集合</param>
    /// <returns>WHERE条件字符串和参数字典</returns>
    public (string WhereClause, Dictionary<string, object> Parameters) BuildWhereClause(List<WhereItem> whereItems)
    {
        _parameters.Clear();
        _parameterIndex = 0;

        if (whereItems == null || !whereItems.Any())
            return ("1=1", new Dictionary<string, object>());

        var conditions = new List<string>();

        foreach (var item in whereItems)
        {
            var condition = BuildSingleCondition(item);
            if (!string.IsNullOrEmpty(condition))
            {
                conditions.Add(condition);
            }
        }

        var whereClause = conditions.Any() ? string.Join(" AND ", conditions) : "1=1";
        return (whereClause, new Dictionary<string, object>(_parameters));
    }

    private string BuildSingleCondition(WhereItem whereItem)
    {
        if (whereItem.Value == null)
            return string.Empty;

        var columnName = GetColumnName(whereItem.DataField);

        return whereItem.Operator switch
        {
            WhereOperator.Equal => BuildEqualCondition(columnName, whereItem.Value),
            WhereOperator.NotEqual => BuildNotEqualCondition(columnName, whereItem.Value),
            WhereOperator.Contains => BuildContainsCondition(columnName, whereItem.Value),
            WhereOperator.StartsWith => BuildStartsWithCondition(columnName, whereItem.Value),
            WhereOperator.EndsWith => BuildEndsWithCondition(columnName, whereItem.Value),
            WhereOperator.GreaterThan => BuildGreaterThanCondition(columnName, whereItem.Value),
            WhereOperator.GreaterThanOrEqual => BuildGreaterThanOrEqualCondition(columnName, whereItem.Value),
            WhereOperator.LessThan => BuildLessThanCondition(columnName, whereItem.Value),
            WhereOperator.LessThanOrEqual => BuildLessThanOrEqualCondition(columnName, whereItem.Value),
            WhereOperator.In => BuildInCondition(columnName, whereItem.Value),
            WhereOperator.Between => BuildBetweenCondition(columnName, whereItem.Value),
            _ => throw new NotSupportedException($"WhereOperator {whereItem.Operator} is not supported")
        };
    }

    private string BuildEqualCondition(string columnName, object value)
    {
        var paramName = CreateParameter(value);
        return $"{columnName} = {paramName}";
    }

    private string BuildNotEqualCondition(string columnName, object value)
    {
        var paramName = CreateParameter(value);
        return $"{columnName} <> {paramName}";
    }

    private string BuildContainsCondition(string columnName, object value)
    {
        var paramName = CreateParameter($"%{value}%");
        return $"{columnName} LIKE {paramName}";
    }

    private string BuildStartsWithCondition(string columnName, object value)
    {
        var paramName = CreateParameter($"{value}%");
        return $"{columnName} LIKE {paramName}";
    }

    private string BuildEndsWithCondition(string columnName, object value)
    {
        var paramName = CreateParameter($"%{value}");
        return $"{columnName} LIKE {paramName}";
    }

    private string BuildGreaterThanCondition(string columnName, object value)
    {
        var paramName = CreateParameter(value);
        return $"{columnName} > {paramName}";
    }

    private string BuildGreaterThanOrEqualCondition(string columnName, object value)
    {
        var paramName = CreateParameter(value);
        return $"{columnName} >= {paramName}";
    }

    private string BuildLessThanCondition(string columnName, object value)
    {
        var paramName = CreateParameter(value);
        return $"{columnName} < {paramName}";
    }

    private string BuildLessThanOrEqualCondition(string columnName, object value)
    {
        var paramName = CreateParameter(value);
        return $"{columnName} <= {paramName}";
    }

    private string BuildInCondition(string columnName, object value)
    {
        if (value is not System.Collections.IEnumerable collection)
            throw new ArgumentException("IN operator requires a collection value");

        var values = collection.Cast<object>().ToList();
        if (!values.Any())
            return "1=0"; // 空集合返回false条件

        var paramNames = values.Select(v => CreateParameter(v)).ToList();
        return $"{columnName} IN ({string.Join(", ", paramNames)})";
    }

    private string BuildBetweenCondition(string columnName, object value)
    {
        if (value is not System.Collections.IList list || list.Count != 2)
            throw new ArgumentException("BETWEEN operator requires exactly two values");

        var lowerParam = CreateParameter(list[0]);
        var upperParam = CreateParameter(list[1]);
        return $"{columnName} BETWEEN {lowerParam} AND {upperParam}";
    }

    private string CreateParameter(object? value)
    {
        var paramName = $"@p{_parameterIndex++}";
        _parameters[paramName] = value ?? DBNull.Value;
        return paramName;
    }

    private string GetColumnName(string dataFieldName)
    {
        // 直接使用已经转换好的数据库列名
        return $"{_tableAlias}.{dataFieldName}";
    }
}
