using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

/// <summary>
/// 动态连接排序条件
/// </summary>
/// <typeparam name="TMainEntity">主表实体类型</typeparam>
/// <typeparam name="TJoinEntity">连接表实体类型</typeparam>
public class DynamicJoinSortCriteria<TMainEntity, TJoinEntity>
{
    /// <summary>
    /// 连接条件表达式
    /// </summary>
    public Expression<Func<TMainEntity, TJoinEntity, bool>> JoinCondition { get; set; } = null!;

    /// <summary>
    /// 匹配条件表达式（用于匹配字段代码）
    /// </summary>
    public Expression<Func<TJoinEntity, string, bool>> MatchCondition { get; set; } = null!;

    /// <summary>
    /// 排序字段选择器
    /// </summary>
    public Expression<Func<TJoinEntity, object>> SortFieldSelector { get; set; } = null!;

    /// <summary>
    /// 字段代码
    /// </summary>
    public string FieldCode { get; set; } = null!;

    /// <summary>
    /// 排序方向
    /// </summary>
    public SortDirection SortDirection { get; set; }
}

/// <summary>
/// 简化的动态排序条件（用于字符串字段匹配）
/// </summary>
public class SimpleDynamicSortCriteria
{
    /// <summary>
    /// 字段代码
    /// </summary>
    public string FieldCode { get; set; } = null!;

    /// <summary>
    /// 排序方向
    /// </summary>
    public SortDirection SortDirection { get; set; }

    /// <summary>
    /// 连接表名
    /// </summary>
    public string JoinTableName { get; set; } = null!;

    /// <summary>
    /// 连接条件字段映射（主表字段 -> 连接表字段）
    /// </summary>
    public Dictionary<string, string> JoinFieldMappings { get; set; } = new();

    /// <summary>
    /// 匹配字段名（连接表中用于匹配FieldCode的字段）
    /// </summary>
    public string MatchFieldName { get; set; } = null!;

    /// <summary>
    /// 排序字段名（连接表中用于排序的字段）
    /// </summary>
    public string SortFieldName { get; set; } = null!;

    /// <summary>
    /// 连接表的软删除字段名（可选）
    /// </summary>
    public string? DeletedFieldName { get; set; }
}
