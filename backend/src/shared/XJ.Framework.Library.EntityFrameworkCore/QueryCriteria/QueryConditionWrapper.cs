using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

/// <summary>
/// 查询条件包装器，用于在不同层之间传递查询条件
/// </summary>
/// <typeparam name="TEntity">实体类型</typeparam>
public class QueryConditionWrapper<TEntity> where TEntity : class
{
    /// <summary>
    /// WHERE条件表达式
    /// </summary>
    public Expression<Func<TEntity, bool>>? WhereExpression { get; set; }

    /// <summary>
    /// 传统排序条件
    /// </summary>
    public List<OrderbyDirection<TEntity>> OrderByConditions { get; set; } = new();

    /// <summary>
    /// 动态连接排序条件
    /// </summary>
    public List<SimpleDynamicSortCriteria> DynamicSortConditions { get; set; } = new();

    /// <summary>
    /// 分页信息
    /// </summary>
    public PageInfo PageInfo { get; set; } = new();
}

/// <summary>
/// 分页信息
/// </summary>
public class PageInfo
{
    /// <summary>
    /// 起始行索引
    /// </summary>
    public int RowIndex { get; set; }

    /// <summary>
    /// 页面大小
    /// </summary>
    public int PageSize { get; set; }
}

/// <summary>
/// 查询条件构建器
/// </summary>
public static class QueryConditionBuilder
{
    /// <summary>
    /// 创建FormInstanceData表的动态排序条件
    /// </summary>
    public static SimpleDynamicSortCriteria CreateFormInstanceDataSort(string fieldCode, SortDirection sortDirection)
    {
        return new SimpleDynamicSortCriteria
        {
            FieldCode = fieldCode,
            SortDirection = sortDirection,
            JoinTableName = "form_instance_datas",
            JoinFieldMappings = new Dictionary<string, string>
            {
                ["BusinessId"] = "business_id",
                ["Version"] = "version",
                ["FormCode"] = "form_code",
                ["FormVersion"] = "form_version"
            },
            MatchFieldName = "code",
            SortFieldName = "value",
            DeletedFieldName = "is_deleted"
        };
    }

    /// <summary>
    /// 创建FormFieldInstance表的动态排序条件
    /// </summary>
    public static SimpleDynamicSortCriteria CreateFormFieldInstanceSort(string fieldCode, SortDirection sortDirection)
    {
        return new SimpleDynamicSortCriteria
        {
            FieldCode = fieldCode,
            SortDirection = sortDirection,
            JoinTableName = "form_field_instances",
            JoinFieldMappings = new Dictionary<string, string>
            {
                ["BusinessId"] = "business_id",
                ["Version"] = "version",
                ["FormCode"] = "form_code",
                ["FormVersion"] = "form_version"
            },
            MatchFieldName = "code",
            SortFieldName = "json_value",
            DeletedFieldName = "is_deleted"
        };
    }
}
