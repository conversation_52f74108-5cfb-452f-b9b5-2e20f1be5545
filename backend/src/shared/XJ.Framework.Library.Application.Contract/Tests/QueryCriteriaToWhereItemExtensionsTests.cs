using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.EntityFrameworkCore.Enums;

namespace XJ.Framework.Library.Application.Contract.Tests;

/// <summary>
/// QueryCriteriaToWhereItemExtensions测试类
/// </summary>
public class QueryCriteriaToWhereItemExtensionsTests
{
    /// <summary>
    /// 测试实体
    /// </summary>
    public class TestEntity : BaseEntity<long>
    {
        public string Name { get; set; } = null!;
        public int Age { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// 测试查询条件
    /// </summary>
    public class TestQueryCriteria : BaseQueryCriteria
    {
        [QueryOperator(QueryOperator.Contains)]
        public string? Name { get; set; }

        [QueryOperator(QueryOperator.GreaterThanOrEqual)]
        public int? MinAge { get; set; }

        [QueryOperator(QueryOperator.LessThanOrEqual, PropertyName = "Age")]
        public int? MaxAge { get; set; }

        [QueryOperator(QueryOperator.Between, PropertyName = "CreatedDate")]
        public List<DateTime>? DateRange { get; set; }

        [QueryOperator(QueryOperator.In, PropertyName = "Age")]
        public List<int>? AgeList { get; set; }

        [QueryOperator(QueryOperator.Equal)]
        public bool? IsActive { get; set; }
    }

    /// <summary>
    /// 测试基本查询条件转换
    /// </summary>
    public void TestBasicQueryCriteriaConversion()
    {
        var criteria = new TestQueryCriteria
        {
            Name = "test",
            MinAge = 18,
            MaxAge = 65,
            IsActive = true
        };

        var whereItems = criteria.BuildWhereItems<long, TestEntity, TestQueryCriteria>();

        // 验证结果
        // Assert.Equal(4, whereItems.Count);
        
        // 验证Name条件
        var nameItem = whereItems.FirstOrDefault(w => w.Field == "Name");
        // Assert.NotNull(nameItem);
        // Assert.Equal(WhereOperator.Contains, nameItem.Operator);
        // Assert.Equal("test", nameItem.Value);

        // 验证MinAge条件
        var minAgeItem = whereItems.FirstOrDefault(w => w.Field == "MinAge");
        // Assert.NotNull(minAgeItem);
        // Assert.Equal(WhereOperator.GreaterThanOrEqual, minAgeItem.Operator);
        // Assert.Equal(18, minAgeItem.Value);

        // 验证MaxAge条件（注意PropertyName映射）
        var maxAgeItem = whereItems.FirstOrDefault(w => w.Field == "Age");
        // Assert.NotNull(maxAgeItem);
        // Assert.Equal(WhereOperator.LessThanOrEqual, maxAgeItem.Operator);
        // Assert.Equal(65, maxAgeItem.Value);

        // 验证IsActive条件
        var isActiveItem = whereItems.FirstOrDefault(w => w.Field == "IsActive");
        // Assert.NotNull(isActiveItem);
        // Assert.Equal(WhereOperator.Equal, isActiveItem.Operator);
        // Assert.Equal(true, isActiveItem.Value);
    }

    /// <summary>
    /// 测试集合类型查询条件
    /// </summary>
    public void TestCollectionQueryCriteria()
    {
        var criteria = new TestQueryCriteria
        {
            DateRange = new List<DateTime>
            {
                new DateTime(2023, 1, 1),
                new DateTime(2023, 12, 31)
            },
            AgeList = new List<int> { 20, 25, 30, 35 }
        };

        var whereItems = criteria.BuildWhereItems<long, TestEntity, TestQueryCriteria>();

        // 验证Between条件
        var dateRangeItem = whereItems.FirstOrDefault(w => w.Field == "CreatedDate");
        // Assert.NotNull(dateRangeItem);
        // Assert.Equal(WhereOperator.Between, dateRangeItem.Operator);
        // Assert.IsType<List<object>>(dateRangeItem.Value);

        // 验证In条件
        var ageListItem = whereItems.FirstOrDefault(w => w.Field == "Age");
        // Assert.NotNull(ageListItem);
        // Assert.Equal(WhereOperator.In, ageListItem.Operator);
        // Assert.IsType<List<int>>(ageListItem.Value);
    }

    /// <summary>
    /// 测试空值处理
    /// </summary>
    public void TestNullValueHandling()
    {
        var criteria = new TestQueryCriteria
        {
            Name = null,
            MinAge = null,
            DateRange = null,
            AgeList = new List<int>() // 空集合
        };

        var whereItems = criteria.BuildWhereItems<long, TestEntity, TestQueryCriteria>();

        // 空值和空集合应该被过滤掉
        // Assert.Empty(whereItems);
    }
}
