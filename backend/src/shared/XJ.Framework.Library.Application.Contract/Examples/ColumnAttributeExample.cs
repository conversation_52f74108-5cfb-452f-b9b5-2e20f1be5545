using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.Application.Contract.Examples;

/// <summary>
/// Column特性使用示例
/// </summary>
public class ColumnAttributeExample
{
    /// <summary>
    /// 示例实体 - 使用Column特性指定数据库列名
    /// </summary>
    public class UserEntity : BaseEntity<long>
    {
        [Column("user_name")]
        public string Name { get; set; } = null!;

        [Column("user_age")]
        public int Age { get; set; }

        [Column("email_address")]
        public string Email { get; set; } = null!;

        [Column("is_active")]
        public bool IsActive { get; set; }

        [Column("created_at")]
        public DateTime CreatedTime { get; set; }

        [Column("updated_at")]
        public DateTime? LastModifiedTime { get; set; }

        // 没有Column特性的属性，会自动转换为snake_case
        public string PhoneNumber { get; set; } = null!; // 会转换为 phone_number
    }

    /// <summary>
    /// 示例查询条件
    /// </summary>
    public class UserQueryCriteria : BaseQueryCriteria
    {
        [QueryOperator(QueryOperator.Contains)]
        public string? Name { get; set; }

        [QueryOperator(QueryOperator.GreaterThanOrEqual)]
        public int? Age { get; set; }

        [QueryOperator(QueryOperator.Equal)]
        public string? Email { get; set; }

        [QueryOperator(QueryOperator.Equal)]
        public bool? IsActive { get; set; }

        [QueryOperator(QueryOperator.Between)]
        public List<DateTime>? CreatedTimeRange { get; set; }

        [QueryOperator(QueryOperator.Contains)]
        public string? PhoneNumber { get; set; }
    }

    /// <summary>
    /// 演示Column特性的使用
    /// </summary>
    public static void DemonstrateColumnAttributeUsage()
    {
        var criteria = new UserQueryCriteria
        {
            Name = "admin",
            Age = 18,
            Email = "<EMAIL>",
            IsActive = true,
            CreatedTimeRange = new List<DateTime>
            {
                new DateTime(2023, 1, 1),
                new DateTime(2023, 12, 31)
            },
            PhoneNumber = "138"
        };

        // 转换为WhereItem集合
        var whereItems = criteria.BuildWhereItems<long, UserEntity, UserQueryCriteria>();

        // 输出结果示例：
        foreach (var item in whereItems)
        {
            Console.WriteLine($"Field: {item.Field}, DataField: {item.DataField}, Operator: {item.Operator}, Value: {item.Value}");
        }

        /*
         * 预期输出：
         * Field: Name, DataField: user_name, Operator: Contains, Value: admin
         * Field: Age, DataField: user_age, Operator: GreaterThanOrEqual, Value: 18
         * Field: Email, DataField: email_address, Operator: Equal, Value: <EMAIL>
         * Field: IsActive, DataField: is_active, Operator: Equal, Value: True
         * Field: CreatedTimeRange, DataField: created_at, Operator: Between, Value: [2023/1/1, 2023/12/31]
         * Field: PhoneNumber, DataField: phone_number, Operator: Contains, Value: 138
         */
    }

    /// <summary>
    /// 演示生成的SQL WHERE条件
    /// </summary>
    public static void DemonstrateSqlGeneration()
    {
        var criteria = new UserQueryCriteria
        {
            Name = "admin",
            Age = 18,
            IsActive = true
        };

        var whereItems = criteria.BuildWhereItems<long, UserEntity, UserQueryCriteria>();

        // 使用WhereItemBuilder生成SQL
        var builder = new XJ.Framework.Library.EntityFrameworkCore.SQL.WhereItemBuilder("u");
        var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

        Console.WriteLine($"WHERE Clause: {whereClause}");
        Console.WriteLine("Parameters:");
        foreach (var param in parameters)
        {
            Console.WriteLine($"  {param.Key} = {param.Value}");
        }

        /*
         * 预期输出：
         * WHERE Clause: u.user_name LIKE @p0 AND u.user_age >= @p1 AND u.is_active = @p2
         * Parameters:
         *   @p0 = %admin%
         *   @p1 = 18
         *   @p2 = True
         */
    }
}

/// <summary>
/// 复杂实体示例 - 展示不同类型的Column特性使用
/// </summary>
public class ComplexEntity : BaseEntity<long>
{
    // 标准的Column特性使用
    [Column("product_name")]
    public string ProductName { get; set; } = null!;

    // 指定数据类型的Column特性
    [Column("price", TypeName = "decimal(18,2)")]
    public decimal Price { get; set; }

    // 指定顺序的Column特性
    [Column("category_id", Order = 1)]
    public int CategoryId { get; set; }

    // 没有Column特性，使用默认转换
    public string Description { get; set; } = null!; // 转换为 description

    // 复杂的属性名转换
    public string SKUCode { get; set; } = null!; // 转换为 sku_code
    public bool IsInStock { get; set; } // 转换为 is_in_stock
    public DateTime LastUpdatedTime { get; set; } // 转换为 last_updated_time
}
