using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.EntityFrameworkCore.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.Enums;
using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;
using System.ComponentModel.DataAnnotations.Schema;

namespace XJ.Framework.Library.Application.Contract.Extensions;

public static class QueryCriteriaToWhereItemExtensions
{
    public static List<WhereItem> BuildWhereItems<TKey, TEntity, TQueryCriteria>(
        this TQueryCriteria criteria)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
        where TQueryCriteria : BaseQueryCriteria
    {
        if (criteria == null)
            return new List<WhereItem>();

        var whereItems = new List<WhereItem>();
        var properties = typeof(TQueryCriteria).GetProperties();

        foreach (var property in properties)
        {
            var queryOperator = property.GetCustomAttributes(typeof(QueryOperatorAttribute), true)
                .FirstOrDefault() as QueryOperatorAttribute;

            if (queryOperator == null) continue;

            var propertyName = queryOperator.PropertyName ?? property.Name;
            var propertyValue = property.GetValue(criteria);

            try
            {
                var whereItem = BuildWhereItem<TEntity>(queryOperator.Operator, propertyName, propertyValue);
                if (whereItem != null)
                    whereItems.Add(whereItem);
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException(
                    $"Error building where item for property '{propertyName}' with operator '{queryOperator.Operator}'",
                    ex);
            }
        }

        return whereItems;
    }

    private static WhereItem? BuildWhereItem<TEntity>(
        QueryOperator queryOperator,
        string propertyName,
        object? operatorValue)
    {
        if (operatorValue == null) return null;

        var entityProperty = typeof(TEntity).GetProperty(propertyName);
        if (entityProperty == null)
            throw new InvalidOperationException(
                $"Property '{propertyName}' not found on type '{typeof(TEntity).Name}'.");

        // 转换QueryOperator到WhereOperator
        var whereOperator = ConvertToWhereOperator(queryOperator);

        // 处理特殊情况
        if (queryOperator == QueryOperator.In && operatorValue is IEnumerable collection)
        {
            return HandleCollectionValue(propertyName, whereOperator, collection, entityProperty.PropertyType);
        }

        if (queryOperator == QueryOperator.Between && operatorValue is IList list)
        {
            return HandleBetweenValue(propertyName, whereOperator, list, entityProperty.PropertyType);
        }

        // 处理字符串类型的特殊情况
        if (entityProperty.PropertyType == typeof(string))
        {
            return HandleStringValue(propertyName, whereOperator, operatorValue);
        }

        // 处理其他类型
        return HandleGeneralValue(propertyName, whereOperator, operatorValue, entityProperty.PropertyType);
    }

    private static WhereOperator ConvertToWhereOperator(QueryOperator queryOperator)
    {
        return queryOperator switch
        {
            QueryOperator.Equal => WhereOperator.Equal,
            QueryOperator.NotEqual => WhereOperator.NotEqual,
            QueryOperator.Contains => WhereOperator.Contains,
            QueryOperator.GreaterThan => WhereOperator.GreaterThan,
            QueryOperator.GreaterThanOrEqual => WhereOperator.GreaterThanOrEqual,
            QueryOperator.LessThan => WhereOperator.LessThan,
            QueryOperator.LessThanOrEqual => WhereOperator.LessThanOrEqual,
            QueryOperator.StartsWith => WhereOperator.StartsWith,
            QueryOperator.EndsWith => WhereOperator.EndsWith,
            QueryOperator.Between => WhereOperator.Between,
            QueryOperator.In => WhereOperator.In,
            _ => throw new NotSupportedException($"QueryOperator {queryOperator} is not supported")
        };
    }

    private static WhereItem? HandleStringValue(string propertyName, WhereOperator whereOperator, object value)
    {
        var stringValue = value.ToString() ?? string.Empty;
        if (string.IsNullOrEmpty(stringValue))
            return null;

        return new WhereItem
        {
            Field = propertyName,
            Operator = whereOperator,
            Value = stringValue
        };
    }

    private static WhereItem? HandleCollectionValue(string propertyName, WhereOperator whereOperator,
        IEnumerable collection, Type propertyType)
    {
        // 处理空集合
        if (collection == null || !collection.Cast<object>().Any())
            return null;

        try
        {
            var targetListType = typeof(List<>).MakeGenericType(propertyType);
            var values = (IList)Activator.CreateInstance(targetListType)!;

            foreach (var item in collection)
            {
                var convertedItem = DataConverter.ChangeType(item?.GetType() ?? typeof(object), item, propertyType);
                if (convertedItem != null)
                {
                    values.Add(convertedItem);
                }
            }

            if (values.Count == 0)
                return null;

            return new WhereItem
            {
                Field = propertyName,
                Operator = whereOperator,
                Value = values
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error processing collection value for property {propertyName}", ex);
        }
    }

    private static WhereItem? HandleBetweenValue(string propertyName, WhereOperator whereOperator,
        IList values, Type propertyType)
    {
        // 验证输入
        if (values == null || values.Count != 2)
        {
            throw new ArgumentException(
                $"Between operator requires exactly two values, got {values?.Count ?? 0} values");
        }

        try
        {
            var lowerValue = DataConverter.ChangeType(values[0]?.GetType() ?? typeof(object), values[0], propertyType);
            var upperValue = DataConverter.ChangeType(values[1]?.GetType() ?? typeof(object), values[1], propertyType);

            if (lowerValue == null || upperValue == null)
                throw new InvalidOperationException($"Cannot convert values to type {propertyType.Name}");

            // 确保下限值小于等于上限值
            if (Comparer<object>.Default.Compare(lowerValue, upperValue) > 0)
            {
                (lowerValue, upperValue) = (upperValue, lowerValue);
            }

            return new WhereItem
            {
                Field = propertyName,
                Operator = whereOperator,
                Value = new List<object> { lowerValue, upperValue }
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Error processing between value for property {propertyName}", ex);
        }
    }

    private static WhereItem? HandleGeneralValue(string propertyName, WhereOperator whereOperator,
        object value, Type propertyType)
    {
        try
        {
            var typedValue = DataConverter.ChangeType(value?.GetType() ?? typeof(object), value, propertyType);
            if (typedValue == null)
                throw new InvalidOperationException($"Cannot convert value to type {propertyType.Name}");

            return new WhereItem
            {
                Field = propertyName,
                Operator = whereOperator,
                Value = typedValue
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException(
                $"Error processing general value for property {propertyName} with operator {whereOperator}", ex);
        }
    }
}
