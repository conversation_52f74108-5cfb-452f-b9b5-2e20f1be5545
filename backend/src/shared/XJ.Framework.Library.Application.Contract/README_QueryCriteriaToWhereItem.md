# QueryCriteria 到 WhereItem 转换功能说明

## 概述

本文档说明了查询条件构建的两阶段拆分实现：
1. **Application层**：将 `QueryCriteria` 转换为 `WhereItem` 集合
2. **EntityFrameworkCore层**：将 `WhereItem` 集合转换为 SQL WHERE 字符串

这种设计实现了架构层次的清晰分离，避免了 EntityFrameworkCore 层直接依赖 Application 层。

## 架构设计

### 第一阶段：QueryCriteria → WhereItem
- **位置**：`XJ.Framework.Library.Application.Contract`
- **文件**：`Extensions/QueryCriteriaToWhereItemExtensions.cs`
- **职责**：解析 QueryCriteria 的属性和特性，转换为中间格式

### 第二阶段：WhereItem → SQL WHERE
- **位置**：`XJ.Framework.Library.EntityFrameworkCore`
- **文件**：`SQL/WhereItemBuilder.cs`
- **职责**：将中间格式转换为 SQL WHERE 条件字符串

## 核心类型

### WhereItem
```csharp
public class WhereItem
{
    public string Field { get; set; }           // 实体属性名
    public string DataField { get; set; }       // 数据库列名
    public WhereOperator Operator { get; set; } // 操作符
    public object? Value { get; set; }          // 值
}
```

**字段说明**：
- `Field`：实体类的属性名，用于调试和日志记录
- `DataField`：实际的数据库列名，用于生成SQL语句
- `Operator`：查询操作符
- `Value`：查询值

### WhereOperator
```csharp
public enum WhereOperator
{
    Equal,
    NotEqual,
    Contains,
    StartsWith,
    EndsWith,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    In,
    Between
}
```

## 使用方法

### 1. 定义查询条件类

```csharp
public class UserQueryCriteria : BaseQueryCriteria
{
    [QueryOperator(QueryOperator.Contains)]
    public string? Name { get; set; }

    [QueryOperator(QueryOperator.GreaterThanOrEqual)]
    public int? MinAge { get; set; }

    [QueryOperator(QueryOperator.LessThanOrEqual, PropertyName = "Age")]
    public int? MaxAge { get; set; }

    [QueryOperator(QueryOperator.Between, PropertyName = "CreatedDate")]
    public List<DateTime>? DateRange { get; set; }

    [QueryOperator(QueryOperator.In, PropertyName = "Status")]
    public List<string>? StatusList { get; set; }

    [QueryOperator(QueryOperator.Equal)]
    public bool? IsActive { get; set; }
}
```

### 2. Application层转换

```csharp
// 在 Application 层
var criteria = new UserQueryCriteria
{
    Name = "admin",
    MinAge = 18,
    MaxAge = 65,
    DateRange = new List<DateTime> { startDate, endDate },
    StatusList = new List<string> { "Active", "Pending" },
    IsActive = true
};

// 转换为 WhereItem 集合
var whereItems = criteria.BuildWhereItems<long, UserEntity, UserQueryCriteria>();
```

### 3. EntityFrameworkCore层转换

```csharp
// 在 EntityFrameworkCore 层
var builder = new WhereItemBuilder("u"); // "u" 是表别名
var (whereClause, parameters) = builder.BuildWhereClause(whereItems);

// 生成的 SQL WHERE 条件示例：
// WHERE u.name LIKE @p0 AND u.min_age >= @p1 AND u.age <= @p2 
//   AND u.created_date BETWEEN @p3 AND @p4 AND u.status IN (@p5, @p6) 
//   AND u.is_active = @p7

// 参数字典：
// @p0 = "%admin%"
// @p1 = 18
// @p2 = 65
// @p3 = startDate
// @p4 = endDate
// @p5 = "Active"
// @p6 = "Pending"
// @p7 = true
```

## 特性说明

### QueryOperatorAttribute
- **QueryOperator**：指定查询操作符
- **PropertyName**：可选，指定映射到实体的属性名（默认使用当前属性名）

### 支持的操作符映射

| QueryOperator | WhereOperator | SQL示例 |
|---------------|---------------|---------|
| Equal | Equal | `field = @p0` |
| NotEqual | NotEqual | `field <> @p0` |
| Contains | Contains | `field LIKE @p0` (值为 `%value%`) |
| StartsWith | StartsWith | `field LIKE @p0` (值为 `value%`) |
| EndsWith | EndsWith | `field LIKE @p0` (值为 `%value`) |
| GreaterThan | GreaterThan | `field > @p0` |
| GreaterThanOrEqual | GreaterThanOrEqual | `field >= @p0` |
| LessThan | LessThan | `field < @p0` |
| LessThanOrEqual | LessThanOrEqual | `field <= @p0` |
| In | In | `field IN (@p0, @p1, @p2)` |
| Between | Between | `field BETWEEN @p0 AND @p1` |

## 类型处理

### 字符串类型
- 自动处理空值和空字符串
- LIKE 操作符自动添加通配符

### 集合类型
- **In操作符**：支持任意类型的集合
- **Between操作符**：要求恰好两个值，自动排序确保下限 ≤ 上限
- 空集合会被过滤掉

### 数值和日期类型
- 自动类型转换
- 支持可空类型

## 命名转换

### 数据库列名映射

系统支持两种方式获取数据库列名：

#### 1. Column特性（推荐）
使用 `[Column]` 特性明确指定数据库列名：

```csharp
public class UserEntity : BaseEntity<long>
{
    [Column("user_name")]
    public string Name { get; set; }

    [Column("email_address")]
    public string Email { get; set; }

    [Column("is_active")]
    public bool IsActive { get; set; }
}
```

#### 2. 自动转换（备选）
没有Column特性时，自动将Pascal命名转换为snake_case：
- `FirstName` → `first_name`
- `LastModifiedTime` → `last_modified_time`
- `IsActive` → `is_active`
- `SKUCode` → `sku_code`

### 表别名
通过 `WhereItemBuilder` 构造函数指定表别名：
```csharp
var builder = new WhereItemBuilder("user_table");
// 生成：user_table.first_name = @p0
```

## 错误处理

### 常见异常
1. **属性不存在**：实体类中找不到对应属性
2. **类型转换失败**：值无法转换为目标类型
3. **Between参数错误**：Between 操作符需要恰好两个值
4. **In参数错误**：In 操作符需要集合类型

### 异常信息
所有异常都包含详细的上下文信息，便于调试：
```csharp
throw new InvalidOperationException(
    $"Error building where item for property '{propertyName}' with operator '{operator}'", 
    innerException);
```

## 扩展建议

1. **自定义操作符**：可以扩展 `WhereOperator` 枚举支持更多操作符
2. **自定义命名转换**：可以自定义属性名到列名的转换规则
3. **复杂条件**：可以支持 OR 条件和嵌套条件
4. **性能优化**：可以添加表达式缓存机制

## 测试

项目包含完整的单元测试：
- `QueryCriteriaToWhereItemExtensionsTests.cs`
- `WhereItemBuilderTests.cs`

测试覆盖了所有操作符、类型转换、边界条件和错误处理场景。
