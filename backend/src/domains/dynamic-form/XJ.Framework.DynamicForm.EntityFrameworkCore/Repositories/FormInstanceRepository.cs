using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.EntityFrameworkCore.Extensions;
using XJ.Framework.Library.EntityFrameworkCore.QueryBuilders;

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Repositories;

/// <summary>
/// FormInstance 仓储实现
/// </summary>
public class FormInstanceRepository : BaseSoftDeleteRepository<DynamicFormDbContext, long, FormInstanceEntity>,
    IFormInstanceRepository
{
    public FormInstanceRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        int rowIndex,
        int pageSize,
        List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        // 构建查询条件
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));

        var queryable = await GetQueryableAsync();
        var baseQuery = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime));

        // 应用排序逻辑 - 三种互斥的排序方式，按优先级处理
        IQueryable<FormInstanceEntity> sortedQuery;

        if (orderBy?.Any() == true)
        {
            // 优先级1: 使用传统的orderBy排序
            sortedQuery = baseQuery.Orderby<long, FormInstanceEntity>(orderBy);
        }
        else if (formDataDynamicOrderBys?.Any() == true)
        {
            // 优先级2: 使用formDataDynamicOrderBys排序（基于FormInstanceData表）
            sortedQuery = baseQuery.ApplyDynamicJoinSorts<FormInstanceEntity, FormInstanceDataEntity>(
                DbContext,
                // 连接条件：BusinessId=BusinessId, Version=Version, FormCode=FormCode, FormVersion=FormVersion
                (main, data) =>
                    data.BusinessId.ToLower().Equals(main.BusinessId.ToLower()) &&
                    data.Version.ToLower().Equals(main.Version.ToLower()) &&
                    data.FormCode.ToLower().Equals(main.FormCode.ToLower()) &&
                    data.FormVersion.ToLower().Equals(main.FormVersion.ToLower()) &&
                    !data.Deleted,
                // 匹配条件：根据Code字段匹配
                (data, fieldCode) => data.Code == fieldCode,
                // 排序字段：Value字段
                data => data.Value ?? string.Empty,
                formDataDynamicOrderBys,
                isFirst: true);
        }
        else if (dynamicOrderBys?.Any() == true)
        {
            // 优先级3: 使用dynamicOrderBys排序（基于FormFieldInstance表）
            sortedQuery = baseQuery.ApplyDynamicJoinSorts<FormInstanceEntity, FormFieldInstanceEntity>(
                DbContext,
                // 连接条件：BusinessId=BusinessId, Version=Version, FormCode=FormCode, FormVersion=FormVersion
                (main, field) =>
                    field.BusinessId.ToLower().Equals(main.BusinessId.ToLower()) &&
                    field.Version.ToLower().Equals(main.Version.ToLower()) &&
                    field.FormCode.ToLower().Equals(main.FormCode.ToLower()) &&
                    field.FormVersion.ToLower().Equals(main.FormVersion.ToLower()) &&
                    !field.Deleted,
                // 匹配条件：根据Code字段匹配
                (field, fieldCode) => field.Code == fieldCode,
                // 排序字段：JsonValue字段
                field => field.JsonValue ?? string.Empty,
                dynamicOrderBys,
                isFirst: true);
        }
        else
        {
            // 默认按主键排序
            sortedQuery = baseQuery.OrderBy(e => e.Key);
        }

        // 先计算总数，再分页
        var totalCount = await baseQuery.CountAsync();
        var rows = await sortedQuery.Skip(rowIndex).Take(pageSize).ToListAsync();

        return new PageData<long, FormInstanceEntity>
        {
            Totals = totalCount,
            Rows = rows
        };
    }

    public async Task<PageData<long, FormInstanceEntity>> GetPageAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda, Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries, int rowIndex,
        int pageSize, List<OrderbyDirection<FormInstanceEntity>> orderBy,
        bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(BuildDynamicQueries(dynamicQueries));
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));
        return await GetPageAsync(whereLambda, rowIndex, pageSize, orderBy, isNoTracking);
    }

    private Expression<Func<FormInstanceEntity, bool>> BuildFormDataDynamicQueries(
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        formDataDynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var op = kv.Operator;
            var value = kv.Value;

            expr = expr.And(formInstance =>
                DbContext.Set<FormInstanceDataEntity>().Any(data =>
                    data.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    data.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    data.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    data.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    data.Code == key &&
                    !data.Deleted &&
                    (op == FormDataQueryOperator.Equal ? data.Value != null && data.Value.Equals(value) :
                        op == FormDataQueryOperator.NotEqual ? data.Value != null && !data.Value.Equals(value) :
                        op == FormDataQueryOperator.Empty ? data.Value == null || string.IsNullOrEmpty(data.Value) :
                        op == FormDataQueryOperator.NotEmpty ? data.Value != null && !string.IsNullOrEmpty(data.Value) :
                        false)
                )
            );
        });
        return expr;
    }


    private Expression<Func<FormInstanceEntity, bool>> BuildDynamicQueries(Dictionary<string, string> dynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        dynamicQueries.ForEach(kv =>
        {
            var key = kv.Key;
            var value = kv.Value;
            if (value.IsNullOrEmpty())
                return;

            expr = expr.And(formInstance =>
                DbContext.Set<FormFieldInstanceEntity>().Any(fieldInstance =>
                    fieldInstance.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
                    fieldInstance.Version.ToLower().Equals(formInstance.Version.ToLower()) &&
                    fieldInstance.FormCode.ToLower().Equals(formInstance.FormCode.ToLower()) &&
                    fieldInstance.FormVersion.ToLower().Equals(formInstance.FormVersion.ToLower()) &&
                    fieldInstance.Code == key &&
                    fieldInstance.JsonValue != null && fieldInstance.JsonValue.Contains(value) &&
                    !fieldInstance.Deleted
                )
            );
        });

        return expr;
    }

    public async Task<int> GetCountAsync(Expression<Func<FormInstanceEntity, bool>> whereLambda,
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        whereLambda = whereLambda.And(q => !q.IsObsoleted);
        whereLambda = whereLambda.And(BuildFormDataDynamicQueries(formDataDynamicQueries));


        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Where(q => !queryable.Where(f =>
                    f.Key != q.Key && !f.IsObsoleted && q.BusinessId.ToLower().Equals(f.BusinessId.ToLower()))
                .Any(f => f.VersionTime > q.VersionTime));
        return await data.CountAsync();
    }

    /// <summary>
    /// 使用原生SQL的备选分页查询方法
    /// </summary>
    public async Task<PageData<long, FormInstanceEntity>> GetNewestPageBySqlAsync(
        Expression<Func<FormInstanceEntity, bool>> whereLambda,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys,
        Dictionary<string, SortDirection> entityOrderBys,
        int rowIndex,
        int pageSize)
    {
        //TODO
        return null;
    }

}
