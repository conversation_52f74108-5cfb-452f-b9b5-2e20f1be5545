<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props" />
    <Import Project="..\..\..\..\Common.props" />

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.EntityFrameworkCore\XJ.Framework.Library.EntityFrameworkCore.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj" />
        <ProjectReference Include="..\XJ.Framework.DynamicForm.Domain\XJ.Framework.DynamicForm.Domain.csproj" />
    </ItemGroup>


</Project> 