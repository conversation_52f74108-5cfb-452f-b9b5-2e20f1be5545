# FormInstance 动态排序功能说明

## 概述

本文档说明了 `FormInstanceRepository.GetNewestPageAsync` 方法中新增的动态排序功能。该功能支持基于从表数据的动态排序，并提供了两种实现方案：EntityFramework Core 表达式和原生 SQL 查询。

## 功能特性

### 1. 三种互斥的排序方式

排序优先级（从高到低）：
1. **orderBy** - 传统的实体属性排序（优先级最高）
2. **formDataDynamicOrderBys** - 基于 FormInstanceData 表的动态排序（优先级中等）
3. **dynamicOrderBys** - 基于 FormFieldInstance 表的动态排序（优先级最低）

### 2. 从表关联排序

支持根据从表数据进行排序：
- **FormInstanceData** 表：根据 `Value` 字段排序
- **FormFieldInstance** 表：根据 `JsonValue` 字段排序

关联条件：
- BusinessId = BusinessId
- Version = Version  
- FormCode = FormCode
- FormVersion = FormVersion
- Code = Dictionary.Key

### 3. 先排序后分页

确保分页逻辑在排序完成后执行，保证数据的正确性。

## 使用方法

### 基本用法

```csharp
// 准备动态排序条件
var dynamicOrderBys = new Dictionary<string, SortDirection>
{
    ["fieldCode1"] = SortDirection.Ascending,
    ["fieldCode2"] = SortDirection.Descending
};

var formDataDynamicOrderBys = new Dictionary<string, SortDirection>
{
    ["dataCode1"] = SortDirection.Ascending
};

// 调用查询方法
var result = await repository.GetNewestPageAsync(
    whereLambda: q => q.FormCode == "demo",
    dynamicQueries: new Dictionary<string, string>(),
    formDataDynamicQueries: new List<FormDataDynamicQuery>(),
    dynamicOrderBys: dynamicOrderBys,
    formDataDynamicOrderBys: formDataDynamicOrderBys,
    rowIndex: 0,
    pageSize: 20,
    orderBy: new List<OrderbyDirection<FormInstanceEntity>>()
);
```

### 使用 SQL 备选方案

```csharp
// 使用原生 SQL 查询（适用于复杂排序场景）
var result = await repository.GetNewestPageBySqlAsync(
    whereLambda: q => q.FormCode == "demo",
    dynamicQueries: new Dictionary<string, string>(),
    formDataDynamicQueries: new List<FormDataDynamicQuery>(),
    dynamicOrderBys: dynamicOrderBys,
    formDataDynamicOrderBys: formDataDynamicOrderBys,
    rowIndex: 0,
    pageSize: 20,
    orderBy: new List<OrderbyDirection<FormInstanceEntity>>()
);
```

## 架构设计

### 核心组件

1. **DynamicSortExtensions** - 通用动态排序扩展方法
2. **SqlQueryBuilder** - 原生 SQL 查询构建器
3. **DynamicSortInfo** - 动态排序信息定义

### 设计原则

1. **通用性**：排序逻辑通过表达式参数化，不依赖特定枚举
2. **可配置性**：连接条件、匹配条件、排序字段都可以通过表达式配置
3. **分离关注点**：排序逻辑与查询条件分离，职责清晰

## 排序逻辑流程

```
1. 检查是否有 orderBy 排序条件
   ├─ 有：使用传统 EF Core 排序
   └─ 无：继续检查动态排序

2. 检查 formDataDynamicOrderBys
   ├─ 有：使用 FormInstanceData 表排序
   └─ 无：继续检查 dynamicOrderBys

3. 检查 dynamicOrderBys
   ├─ 有：使用 FormFieldInstance 表排序
   └─ 无：使用默认主键排序

4. 应用排序并分页
   ├─ EF Core 方式：使用动态表达式
   └─ SQL 方式：使用原生 SQL 查询
```

## 扩展方法详解

### ApplyDynamicJoinSort

单个动态连接排序：

```csharp
queryable.ApplyDynamicJoinSort<FormInstanceEntity, FormInstanceDataEntity>(
    DbContext,
    // 连接条件
    (main, data) => data.BusinessId == main.BusinessId && ...,
    // 匹配条件
    (data, fieldCode) => data.Code == fieldCode,
    // 排序字段
    data => data.Value ?? string.Empty,
    fieldCode,
    sortDirection,
    isFirst: true
);
```

### ApplyDynamicJoinSorts

批量动态连接排序：

```csharp
queryable.ApplyDynamicJoinSorts<FormInstanceEntity, FormInstanceDataEntity>(
    DbContext,
    joinCondition,
    matchCondition,
    sortSelector,
    sortFieldsDictionary,
    isFirst: true
);
```

## 性能考虑

1. **索引优化**：确保从表的关联字段和排序字段有适当的索引
2. **查询优化**：对于复杂排序场景，SQL 方案可能有更好的性能
3. **缓存策略**：考虑对频繁查询的排序结果进行缓存

## 注意事项

1. 动态排序基于文本值进行比较，数值排序可能需要特殊处理
2. 确保从表数据的完整性，避免因数据缺失导致排序异常
3. SQL 方案中的 WHERE 条件转换是简化实现，复杂条件可能需要扩展

## 扩展建议

1. 可以添加数值类型的智能排序支持
2. 可以实现更复杂的表达式到SQL转换
3. 可以添加排序性能监控和优化建议
4. 可以支持更多的连接表类型和排序字段
