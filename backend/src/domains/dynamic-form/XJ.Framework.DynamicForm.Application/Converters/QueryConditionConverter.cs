using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.EntityFrameworkCore.QueryCriteria;

namespace XJ.Framework.DynamicForm.Application.Converters;

/// <summary>
/// 查询条件转换器，将Application层的查询条件转换为EntityFrameworkCore层可用的格式
/// </summary>
public static class QueryConditionConverter
{
    /// <summary>
    /// 将PagedQueryCriteria转换为QueryConditionWrapper
    /// </summary>
    public static QueryConditionWrapper<FormInstanceEntity> ConvertToQueryWrapper(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria,
        Dictionary<string, SortDirection> dynamicOrderBys,
        Dictionary<string, SortDirection> formDataDynamicOrderBys)
    {
        var wrapper = new QueryConditionWrapper<FormInstanceEntity>
        {
            WhereExpression = criteria.Condition.BuildExpression<long, FormInstanceEntity, FormInstanceQueryCriteria>(),
            OrderByConditions = criteria.BuildOrderExpression<long, FormInstanceEntity, FormInstanceQueryCriteria>(),
            PageInfo = new PageInfo
            {
                RowIndex = criteria.PageParams.ToRowIndex(),
                PageSize = criteria.PageParams.PageSize
            }
        };

        // 转换动态排序条件
        var dynamicSorts = new List<SimpleDynamicSortCriteria>();

        // 优先级2: formDataDynamicOrderBys
        foreach (var item in formDataDynamicOrderBys)
        {
            dynamicSorts.Add(QueryConditionBuilder.CreateFormInstanceDataSort(item.Key, item.Value));
        }

        // 优先级3: dynamicOrderBys
        foreach (var item in dynamicOrderBys)
        {
            dynamicSorts.Add(QueryConditionBuilder.CreateFormFieldInstanceSort(item.Key, item.Value));
        }

        wrapper.DynamicSortConditions = dynamicSorts;

        return wrapper;
    }

    /// <summary>
    /// 合并查询条件，处理动态查询和表单数据查询
    /// </summary>
    public static QueryConditionWrapper<FormInstanceEntity> MergeQueryConditions(
        QueryConditionWrapper<FormInstanceEntity> baseWrapper,
        Dictionary<string, string> dynamicQueries,
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        // 构建动态查询条件
        var dynamicQueryExpression = BuildDynamicQueries(dynamicQueries);
        var formDataQueryExpression = BuildFormDataDynamicQueries(formDataDynamicQueries);

        // 合并WHERE条件
        if (baseWrapper.WhereExpression != null)
        {
            baseWrapper.WhereExpression = baseWrapper.WhereExpression
                .And(dynamicQueryExpression)
                .And(formDataQueryExpression);
        }
        else
        {
            baseWrapper.WhereExpression = dynamicQueryExpression.And(formDataQueryExpression);
        }

        return baseWrapper;
    }

    private static Expression<Func<FormInstanceEntity, bool>> BuildDynamicQueries(Dictionary<string, string> dynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        
        foreach (var kv in dynamicQueries)
        {
            var key = kv.Key;
            var value = kv.Value;
            if (string.IsNullOrEmpty(value))
                continue;

            expr = expr.And(formInstance =>
                // 这里需要访问DbContext，但在Application层无法直接访问
                // 所以这部分逻辑需要在Repository层处理
                true // 临时占位符
            );
        }

        return expr;
    }

    private static Expression<Func<FormInstanceEntity, bool>> BuildFormDataDynamicQueries(
        List<FormDataDynamicQuery> formDataDynamicQueries)
    {
        var expr = DynamicLinqExpressions.True<FormInstanceEntity>();
        
        foreach (var query in formDataDynamicQueries)
        {
            var key = query.Key;
            var op = query.Operator;
            var value = query.Value;

            expr = expr.And(formInstance =>
                // 这里需要访问DbContext，但在Application层无法直接访问
                // 所以这部分逻辑需要在Repository层处理
                true // 临时占位符
            );
        }

        return expr;
    }
}
